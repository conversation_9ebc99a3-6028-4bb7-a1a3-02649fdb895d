package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentTakerDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateQuestionDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.CacheManager;

import java.util.Arrays;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class KafkaServiceIntegrationTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private CandidateAssessmentService candidateAssessmentService;

    @Mock
    private CandidateQuestionService candidateQuestionService;

    @Mock
    private CacheManager cacheManager;

    @InjectMocks
    private KafkaService kafkaService;

    private ConsumerRecord<String, String> progressMessage;
    private ConsumerRecord<String, String> resultsMessage;
    private HashMap<String, Object> progressData;
    private HashMap<String, Object> resultsData;
    private AssessmentTakerDTO assessmentTakerDTO;
    private AssessmentInputDTO assessmentInputDTO;

    @BeforeEach
    void setUp() throws Exception {
        // Setup progress message data (in-progress assessment)
        CandidateQuestionDTO questionDTO = CandidateQuestionDTO.builder()
                .id("question-123")
                .questionText("What is 2+2?")
                .questionType("MULTIPLE_CHOICE")
                .score(10.0)
                .build();

        CandidateTestDTO testDTO = CandidateTestDTO.builder()
                .id("test-456")
                .title("Math Test")
                .questions(Arrays.asList(questionDTO))
                .build();

        CandidateAssessmentDTO assessmentDTO = CandidateAssessmentDTO.builder()
                .id("assessment-789")
                .title("Sample Assessment")
                .tests(Arrays.asList(testDTO))
                .build();

        AssessmentProgressDTO progressDTO = AssessmentProgressDTO.builder()
                .id("candidate-101")
                .assessmentId("assessment-789")
                .organizationId("org-202")
                .email("<EMAIL>")
                .status("IN_PROGRESS")
                .assessment(assessmentDTO)
                .build();

        assessmentTakerDTO = AssessmentTakerDTO.builder()
                .assessmentTaker(progressDTO)
                .build();

        progressData = new HashMap<>();
        progressData.put("assessmentTaker", progressDTO);

        progressMessage = new ConsumerRecord<>("assessment-progress", 0, 0L, "key", "progress-json");

        // Setup results message data (completed assessment)
        QuestionResultInputDTO questionResultDTO = QuestionResultInputDTO.builder()
                .questionId("question-123")
                .questionText("What is 2+2?")
                .questionType("MULTIPLE_CHOICE")
                .score(10)
                .scored(10.0)
                .rawTestTakerAnswersData(Arrays.asList("4"))
                .build();

        TestResultInputDTO testResultDTO = TestResultInputDTO.builder()
                .testId("test-456")
                .questionResults(Arrays.asList(questionResultDTO))
                .totalScore(10)
                .totalPassedScore(10.0)
                .build();

        assessmentInputDTO = AssessmentInputDTO.builder()
                .organizationId("org-202")
                .assessmentId("assessment-789")
                .email("<EMAIL>")
                .testTakerId("candidate-101")
                .status("COMPLETED")
                .testResults(Arrays.asList(testResultDTO))
                .build();

        resultsData = new HashMap<>();
        resultsData.put("organizationId", "org-202");
        resultsData.put("assessmentId", "assessment-789");

        resultsMessage = new ConsumerRecord<>("submit-assessment-results", 0, 0L, "key", "results-json");
    }

    @Test
    void testConsumeAssessmentProgress_OnlySavesMetadata() throws Exception {
        when(objectMapper.readValue(eq("progress-json"), any(Class.class)))
                .thenReturn(progressData);
        when(objectMapper.convertValue(eq(progressData), eq(AssessmentTakerDTO.class)))
                .thenReturn(assessmentTakerDTO);
        doNothing().when(candidateAssessmentService).saveCandidateAssessment(any(AssessmentProgressDTO.class));

        kafkaService.consumeAssessmentProgress(progressMessage);

        // Verify that only assessment metadata is saved (no questions)
        verify(candidateAssessmentService, times(1)).saveCandidateAssessment(eq(assessmentTakerDTO.getAssessmentTaker()));
        
        // Verify that question services are NOT called for progress messages
        verify(candidateQuestionService, never()).saveCandidateQuestionResult(any(AssessmentProgressDTO.class));
        verify(candidateQuestionService, never()).saveCandidateQuestionResultFromAssessmentInput(any(AssessmentInputDTO.class));
        verify(candidateQuestionService, never()).updateCandidateQuestionResult(any(AssessmentInputDTO.class));
    }

    @Test
    void testConsumeAssessmentResults_SavesQuestionsWithAnswers() throws Exception {
        when(objectMapper.readValue(eq("results-json"), any(Class.class)))
                .thenReturn(resultsData);
        when(objectMapper.convertValue(eq(resultsData), eq(AssessmentInputDTO.class)))
                .thenReturn(assessmentInputDTO);
        when(objectMapper.writeValueAsString(any())).thenReturn("converted-json");
        doNothing().when(candidateAssessmentService).updateCandidateAssessment(any(AssessmentInputDTO.class));

        kafkaService.consumeAssessmentResults(resultsMessage);

        // Verify that assessment is updated (which internally saves questions with answers)
        verify(candidateAssessmentService, times(1)).updateCandidateAssessment(eq(assessmentInputDTO));
        
        // Note: The actual saving of questions is tested in CandidateAssessmentImplModifiedMethodsTest
        // This test verifies the Kafka service correctly routes to the update method
    }

    @Test
    void testConsumeAssessmentProgress_HandlesException() throws Exception {
        when(objectMapper.readValue(eq("progress-json"), any(Class.class)))
                .thenThrow(new RuntimeException("JSON parsing error"));

        // Should not throw exception - error should be logged and handled
        kafkaService.consumeAssessmentProgress(progressMessage);

        // Verify that assessment service is not called when there's an error
        verify(candidateAssessmentService, never()).saveCandidateAssessment(any(AssessmentProgressDTO.class));
    }

    @Test
    void testConsumeAssessmentResults_HandlesException() throws Exception {
        when(objectMapper.readValue(eq("results-json"), any(Class.class)))
                .thenThrow(new RuntimeException("JSON parsing error"));

        // Should not throw exception - error should be logged and handled
        kafkaService.consumeAssessmentResults(resultsMessage);

        // Verify that assessment service is not called when there's an error
        verify(candidateAssessmentService, never()).updateCandidateAssessment(any(AssessmentInputDTO.class));
    }

    @Test
    void testConsumeAssessmentProgress_VerifyCorrectDataFlow() throws Exception {
        when(objectMapper.readValue(eq("progress-json"), any(Class.class)))
                .thenReturn(progressData);
        when(objectMapper.convertValue(eq(progressData), eq(AssessmentTakerDTO.class)))
                .thenReturn(assessmentTakerDTO);
        doNothing().when(candidateAssessmentService).saveCandidateAssessment(any(AssessmentProgressDTO.class));

        kafkaService.consumeAssessmentProgress(progressMessage);

        // Verify the correct data transformation and flow
        verify(objectMapper, times(1)).readValue(eq("progress-json"), any(Class.class));
        verify(objectMapper, times(1)).convertValue(eq(progressData), eq(AssessmentTakerDTO.class));
        verify(candidateAssessmentService, times(1)).saveCandidateAssessment(argThat(dto -> 
            "candidate-101".equals(dto.getId()) &&
            "assessment-789".equals(dto.getAssessmentId()) &&
            "org-202".equals(dto.getOrganizationId()) &&
            "<EMAIL>".equals(dto.getEmail()) &&
            "IN_PROGRESS".equals(dto.getStatus())
        ));
    }

    @Test
    void testConsumeAssessmentResults_VerifyCorrectDataFlow() throws Exception {
        when(objectMapper.readValue(eq("results-json"), any(Class.class)))
                .thenReturn(resultsData);
        when(objectMapper.convertValue(eq(resultsData), eq(AssessmentInputDTO.class)))
                .thenReturn(assessmentInputDTO);
        when(objectMapper.writeValueAsString(any())).thenReturn("converted-json");
        doNothing().when(candidateAssessmentService).updateCandidateAssessment(any(AssessmentInputDTO.class));

        kafkaService.consumeAssessmentResults(resultsMessage);

        // Verify the correct data transformation and flow
        verify(objectMapper, times(1)).readValue(eq("results-json"), any(Class.class));
        verify(objectMapper, times(1)).convertValue(eq(resultsData), eq(AssessmentInputDTO.class));
        verify(objectMapper, times(1)).writeValueAsString(eq(assessmentInputDTO));
        verify(candidateAssessmentService, times(1)).updateCandidateAssessment(argThat(dto -> 
            "org-202".equals(dto.getOrganizationId()) &&
            "assessment-789".equals(dto.getAssessmentId()) &&
            "<EMAIL>".equals(dto.getEmail()) &&
            "candidate-101".equals(dto.getTestTakerId()) &&
            "COMPLETED".equals(dto.getStatus())
        ));
    }
}
