package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TestQuestionServiceImplNewMethodsTest {

    @Mock
    private TestQuestionRepository testQuestionRepository;

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private AssessmentRepository assessmentRepository;

    @InjectMocks
    private TestQuestionServiceImpl testQuestionService;

    private AssessmentInputDTO assessmentInputDTO;
    private TestResultInputDTO testResultInputDTO;
    private QuestionResultInputDTO questionResultInputDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        questionResultInputDTO = QuestionResultInputDTO.builder()
                .questionId("question-123")
                .questionText("What is 2+2?")
                .questionType("MULTIPLE_CHOICE")
                .difficultyLevel("EASY")
                .score(10)
                .build();

        testResultInputDTO = TestResultInputDTO.builder()
                .testId("test-456")
                .questionResults(Arrays.asList(questionResultInputDTO))
                .numberOfQuestions(1)
                .totalScore(10)
                .build();

        assessmentInputDTO = AssessmentInputDTO.builder()
                .organizationId("org-789")
                .assessmentId("assessment-101")
                .testResults(Arrays.asList(testResultInputDTO))
                .build();
    }

    @Test
    void testSaveTestQuestions_Success() {
        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(testQuestionRepository).saveTestQuestion(any(TestQuestion.class));

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        verify(testQuestionRepository, times(1)).checkIfExist("org-789", "assessment-101", "test-456", "question-123");
        verify(testQuestionRepository, times(1)).saveTestQuestion(any(TestQuestion.class));
    }

    @Test
    void testSaveTestQuestions_SkipExistingQuestions() {
        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(true); // Question already exists

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        verify(testQuestionRepository, times(1)).checkIfExist("org-789", "assessment-101", "test-456", "question-123");
        verify(testQuestionRepository, never()).saveTestQuestion(any(TestQuestion.class));
    }

    @Test
    void testSaveTestQuestions_MultipleTestsAndQuestions() {
        // Setup multiple questions in first test
        QuestionResultInputDTO question2 = QuestionResultInputDTO.builder()
                .questionId("question-456")
                .questionText("What is 3+3?")
                .questionType("MULTIPLE_CHOICE")
                .difficultyLevel("MEDIUM")
                .score(15)
                .build();

        testResultInputDTO.getQuestionResults().add(question2);

        // Setup second test
        QuestionResultInputDTO question3 = QuestionResultInputDTO.builder()
                .questionId("question-789")
                .questionText("What is 4+4?")
                .questionType("FILL_IN")
                .difficultyLevel("HARD")
                .score(20)
                .build();

        TestResultInputDTO test2 = TestResultInputDTO.builder()
                .testId("test-789")
                .questionResults(Arrays.asList(question3))
                .build();

        assessmentInputDTO.getTestResults().add(test2);

        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(testQuestionRepository).saveTestQuestion(any(TestQuestion.class));

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        // Should save 3 questions total
        verify(testQuestionRepository, times(3)).saveTestQuestion(any(TestQuestion.class));
        verify(testQuestionRepository, times(3)).checkIfExist(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testSaveTestQuestions_VerifyEntityFields() {
        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(testQuestionRepository).saveTestQuestion(any(TestQuestion.class));

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        verify(testQuestionRepository).saveTestQuestion(argThat(testQuestion -> 
            "question-123".equals(testQuestion.getQuestionId()) &&
            "test-456".equals(testQuestion.getTestId()) &&
            "assessment-101".equals(testQuestion.getAssessmentId()) &&
            "org-789".equals(testQuestion.getOrganizationId()) &&
            "What is 2+2?".equals(testQuestion.getQuestionText()) &&
            "MULTIPLE_CHOICE".equals(testQuestion.getQuestionType()) &&
            "EASY".equals(testQuestion.getDifficultyLevel()) &&
            testQuestion.getTotalScore() == 10 &&
            testQuestion.getCreatedAt() != null
        ));
    }

    @Test
    void testSaveTestQuestions_Exception() {
        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);
        doThrow(new RuntimeException("Database error")).when(testQuestionRepository)
                .saveTestQuestion(any(TestQuestion.class));

        assertThrows(ProcessFailedException.class, () -> 
            testQuestionService.saveTestQuestions(assessmentInputDTO)
        );
    }

    @Test
    void testSaveTestQuestions_MixedExistingAndNewQuestions() {
        // Setup multiple questions
        QuestionResultInputDTO question2 = QuestionResultInputDTO.builder()
                .questionId("question-456")
                .questionText("What is 3+3?")
                .questionType("MULTIPLE_CHOICE")
                .score(15)
                .build();

        testResultInputDTO.getQuestionResults().add(question2);

        // First question exists, second doesn't
        when(testQuestionRepository.checkIfExist("org-789", "assessment-101", "test-456", "question-123"))
                .thenReturn(true);
        when(testQuestionRepository.checkIfExist("org-789", "assessment-101", "test-456", "question-456"))
                .thenReturn(false);
        doNothing().when(testQuestionRepository).saveTestQuestion(any(TestQuestion.class));

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        // Should only save the second question
        verify(testQuestionRepository, times(1)).saveTestQuestion(any(TestQuestion.class));
        verify(testQuestionRepository, times(2)).checkIfExist(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testSaveTestQuestions_EmptyTestResults() {
        assessmentInputDTO.setTestResults(Arrays.asList());

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        verify(testQuestionRepository, never()).checkIfExist(anyString(), anyString(), anyString(), anyString());
        verify(testQuestionRepository, never()).saveTestQuestion(any(TestQuestion.class));
    }

    @Test
    void testSaveTestQuestions_EmptyQuestionResults() {
        testResultInputDTO.setQuestionResults(Arrays.asList());

        testQuestionService.saveTestQuestions(assessmentInputDTO);

        verify(testQuestionRepository, never()).checkIfExist(anyString(), anyString(), anyString(), anyString());
        verify(testQuestionRepository, never()).saveTestQuestion(any(TestQuestion.class));
    }
}
