package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.mapper.QuestionMapper;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateQuestionRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CandidateQuestionServiceImplNewMethodsTest {

    @Mock
    private CandidateQuestionRepository candidateQuestionRepository;

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private TestQuestionRepository testQuestionRepository;

    @InjectMocks
    private CandidateQuestionServiceImpl candidateQuestionService;

    private AssessmentInputDTO assessmentInputDTO;
    private TestResultInputDTO testResultInputDTO;
    private QuestionResultInputDTO questionResultInputDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        questionResultInputDTO = QuestionResultInputDTO.builder()
                .questionId("question-123")
                .questionText("What is 2+2?")
                .questionType("MULTIPLE_CHOICE")
                .difficultyLevel("EASY")
                .score(10)
                .scored(10.0)
                .rawTestTakerAnswersData(Arrays.asList("4"))
                .codeReview("Good answer")
                .build();

        testResultInputDTO = TestResultInputDTO.builder()
                .testId("test-456")
                .questionResults(Arrays.asList(questionResultInputDTO))
                .numberOfQuestions(1)
                .totalScore(10)
                .status("SUBMITTED")
                .build();

        assessmentInputDTO = AssessmentInputDTO.builder()
                .organizationId("org-789")
                .assessmentId("assessment-101")
                .email("<EMAIL>")
                .testTakerId("taker-202")
                .testResults(Arrays.asList(testResultInputDTO))
                .status("Completed")
                .build();
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_Success() {
        // Mock the static method
        try (MockedStatic<QuestionMapper> questionMapperMock = mockStatic(QuestionMapper.class)) {
            doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

            // Call the method
            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO);

            // Verify interactions
            verify(candidateQuestionRepository, times(1)).saveCandidateQuestion(any(CandidateQuestionResult.class));
            questionMapperMock.verify(() -> QuestionMapper.mapToCandidateQuestionResult(any(), any()), times(1));
        }
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_WithCorrectAnswer() {
        // Setup question with correct answer (scored equals total score)
        questionResultInputDTO.setScored(10.0);
        questionResultInputDTO.setScore(10);

        try (MockedStatic<QuestionMapper> questionMapperMock = mockStatic(QuestionMapper.class)) {
            doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO);

            verify(candidateQuestionRepository).saveCandidateQuestion(argThat(question -> 
                "CORRECT".equals(question.getIsAnswerCorrect()) && 
                question.isAnswered() == true
            ));
        }
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_WithIncorrectAnswer() {
        // Setup question with incorrect answer
        questionResultInputDTO.setScored(5.0);
        questionResultInputDTO.setScore(10);

        try (MockedStatic<QuestionMapper> questionMapperMock = mockStatic(QuestionMapper.class)) {
            doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO);

            verify(candidateQuestionRepository).saveCandidateQuestion(argThat(question -> 
                question.getIsAnswerCorrect() == null && // Not set to CORRECT
                question.isAnswered() == true
            ));
        }
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_WithNoAnswers() {
        // Setup question with no answers
        questionResultInputDTO.setRawTestTakerAnswersData(null);

        try (MockedStatic<QuestionMapper> questionMapperMock = mockStatic(QuestionMapper.class)) {
            doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO);

            verify(candidateQuestionRepository).saveCandidateQuestion(argThat(question -> 
                question.isAnswered() == false
            ));
        }
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_MultipleTestsAndQuestions() {
        // Setup multiple tests and questions
        QuestionResultInputDTO question2 = QuestionResultInputDTO.builder()
                .questionId("question-456")
                .questionText("What is 3+3?")
                .questionType("MULTIPLE_CHOICE")
                .score(15)
                .scored(15.0)
                .rawTestTakerAnswersData(Arrays.asList("6"))
                .build();

        TestResultInputDTO test2 = TestResultInputDTO.builder()
                .testId("test-789")
                .questionResults(Arrays.asList(question2))
                .build();

        testResultInputDTO.getQuestionResults().add(question2);
        assessmentInputDTO.getTestResults().add(test2);

        try (MockedStatic<QuestionMapper> questionMapperMock = mockStatic(QuestionMapper.class)) {
            doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO);

            // Should save 3 questions total (2 from first test, 1 from second test)
            verify(candidateQuestionRepository, times(3)).saveCandidateQuestion(any(CandidateQuestionResult.class));
        }
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_Exception() {
        doThrow(new RuntimeException("Database error")).when(candidateQuestionRepository)
                .saveCandidateQuestion(any(CandidateQuestionResult.class));

        assertThrows(ProcessFailedException.class, () -> 
            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO)
        );
    }

    @Test
    void testSaveCandidateQuestionResultFromAssessmentInput_VerifyEntityFields() {
        try (MockedStatic<QuestionMapper> questionMapperMock = mockStatic(QuestionMapper.class)) {
            doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

            candidateQuestionService.saveCandidateQuestionResultFromAssessmentInput(assessmentInputDTO);

            verify(candidateQuestionRepository).saveCandidateQuestion(argThat(question -> 
                "org-789".equals(question.getOrganizationId()) &&
                "taker-202".equals(question.getCandidateId()) &&
                "<EMAIL>".equals(question.getCandidateEmail()) &&
                "question-123".equals(question.getQuestionId()) &&
                "What is 2+2?".equals(question.getQuestionText()) &&
                "MULTIPLE_CHOICE".equals(question.getQuestionType()) &&
                "EASY".equals(question.getDifficultyLevel()) &&
                question.getCandidateMarks() == 10.0 &&
                question.getTotalScore() == 10 &&
                question.getCreatedAt() != null
            ));
        }
    }
}
